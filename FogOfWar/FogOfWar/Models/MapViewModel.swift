import SwiftUI
import MapKit
import Combine // Ensure Combine is imported for objectWillChange

@MainActor
final class MapViewModel: NSObject, ObservableObject, MKMapViewDelegate {
    @Published var showOverlays = true
    @Published var showTracks = true
    @Published var showMarkers = true
    @Published var showAreas = true
    @Published var scaleText: String = ""

    private weak var mapView: MKMapView?
    private var lastRegion: MKCoordinateRegion?

    // Default location and zoom level
    private let defaultLocation = CLLocationCoordinate2D(latitude: 47.073692, longitude: -80.103519)
    private let defaultSpan = MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01) // Pretty zoomed in

    /// Current center coordinate of the bound map view, if available.
    var centerCoordinate: CLLocationCoordinate2D? {
        if let mapView = mapView {
            let center = mapView.centerCoordinate
            // Check if we have a valid coordinate (not 0,0 which might indicate uninitialized)
            if center.latitude != 0.0 || center.longitude != 0.0 {
                return center
            }
        }
        // Fall back to default location if map view is not available or has invalid coordinates
        return defaultLocation
    }

    /// Approximate width of the current map view in meters.
    var mapWidthMeters: Double? {
        guard let mapView else { return nil }
        let metersPerPoint = MKMetersPerMapPointAtLatitude(mapView.region.center.latitude)
        return metersPerPoint * Double(mapView.bounds.width)
    }

    /// Width of the current map view in map points.
    var mapWidthPoints: Double? {
        mapView?.visibleMapRect.size.width
    }

    /// Current map region of the view model if available.
    var currentRegion: MKCoordinateRegion? {
        mapView?.region ?? lastRegion
    }

    func setMapView(_ mapView: MKMapView) {
        self.mapView = mapView
        mapView.delegate = self

        if let region = lastRegion {
            print("MapViewModel: Setting map to saved region: \(region.center)")
            mapView.setRegion(region, animated: false)
        } else {
            // Set default location if no previous region
            let defaultRegion = MKCoordinateRegion(center: defaultLocation, span: defaultSpan)
            print("MapViewModel: Setting map to default location: \(defaultLocation)")
            mapView.setRegion(defaultRegion, animated: false)
            lastRegion = defaultRegion
        }
    }

    func recenterUser() {
        mapView?.setUserTrackingMode(.follow, animated: true)
    }

    func resetToDefaultLocation() {
        let defaultRegion = MKCoordinateRegion(center: defaultLocation, span: defaultSpan)
        mapView?.setRegion(defaultRegion, animated: true)
        lastRegion = defaultRegion
        print("MapViewModel: Reset to default location: \(defaultLocation)")
    }

    /// Set the map region to center on a specific coordinate with given dimensions
    func setMapRegion(center: CLLocationCoordinate2D, latitudinalMeters: Double, longitudinalMeters: Double) {
        let region = MKCoordinateRegion(
            center: center,
            latitudinalMeters: latitudinalMeters,
            longitudinalMeters: longitudinalMeters
        )
        mapView?.setRegion(region, animated: true)
        lastRegion = region
        print("MapViewModel: Set region to center: \(center), size: \(latitudinalMeters)m x \(longitudinalMeters)m")
    }

    func mapViewDidChangeVisibleRegion(_ mapView: MKMapView) {
        lastRegion = mapView.region
        updateScaleText(for: mapView)
        // This will publish changes and cause SwiftUI to re-render MapViewRepresentable,
        // which in turn calls updateUIView with the new map center.
        DispatchQueue.main.async {
            self.objectWillChange.send()
            // Also update the published region if other parts of your UI depend on it directly
            // self.region = mapView.region
            print("MapViewModel: mapViewDidChangeVisibleRegion - triggering view update.")
        }
    }

    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        print("MapView delegate called for overlay renderer")
        if let imageOverlay = overlay as? ImageOverlay {
            print("Creating ImageOverlayRenderer for overlay: \(imageOverlay.overlayData.name ?? "Unnamed")")
            return ImageOverlayRenderer(overlay: imageOverlay)
        }
        print("Creating default MKOverlayRenderer")
        return MKOverlayRenderer(overlay: overlay)
    }

    private func updateScaleText(for mapView: MKMapView) {
        let region = mapView.region
        let metersPerPoint = MKMetersPerMapPointAtLatitude(region.center.latitude)
        let mapWidth = metersPerPoint * Double(mapView.bounds.width)
        if mapWidth >= 1000 {
            scaleText = String(format: "%.1f km", mapWidth/1000)
        } else {
            scaleText = String(format: "%.0f m", mapWidth)
        }
    }
}
