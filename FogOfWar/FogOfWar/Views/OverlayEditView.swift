import SwiftUI
import CoreLocation
import CoreData

struct OverlayEditView: View {
    @ObservedObject var overlay: Overlay
    var viewModel: MapViewModel
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    // Always in editing mode - no toggle needed

    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Map view - always in editing mode for simplicity
                MapViewRepresentable(viewModel: viewModel, overlays: [overlay], isEditingPosition: true)
                    .frame(maxWidth: .infinity)
                    .frame(height: geometry.size.height - 280) // Leave 280pt for controls and safe areas
                    .clipped()

                // Controls section - simplified sliders with icons
                VStack(spacing: 16) {
                    // Opacity slider
                    HStack(spacing: 12) {
                        Image(systemName: "eye.fill")
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        Slider(value: Binding(
                            get: { overlay.opacity },
                            set: {
                                overlay.opacity = $0
                                overlayPublisher.overlayDidChange()
                            }
                        ), in: 0...1)
                        Text(String(format: "%.1f", overlay.opacity))
                            .frame(width: 35)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // Rotation slider
                    HStack(spacing: 12) {
                        Image(systemName: "rotate.right.fill")
                            .foregroundColor(.green)
                            .frame(width: 24)
                        Slider(value: Binding(
                            get: { overlay.rotation },
                            set: {
                                overlay.rotation = $0
                                overlayPublisher.overlayDidChange()
                            }
                        ), in: 0...(Double.pi * 2))
                        Text(String(format: "%.0f°", overlay.rotation * 180 / .pi))
                            .frame(width: 35)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // Scale slider
                    HStack(spacing: 12) {
                        Image(systemName: "arrow.up.left.and.arrow.down.right")
                            .foregroundColor(.orange)
                            .frame(width: 24)
                        Slider(value: Binding(
                            get: { overlay.relativeScale },
                            set: {
                                overlay.relativeScale = $0
                                overlayPublisher.overlayDidChange()
                            }
                        ), in: 100...10000)
                        Text(String(format: "%.0fm", overlay.relativeScale))
                            .frame(width: 35)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .frame(height: 160) // Fixed height for controls
                .background(.regularMaterial.opacity(0.8)) // 80% opacity to see map behind
            }
        }
        .navigationTitle("Edit Overlay")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    // Save any remaining changes (position is already saved when stopping editing)
                    overlay.updatedAt = Date()
                    do {
                        try viewContext.save()
                        print("Overlay saved.")

                        // Notify that the overlay has changed so Map Screen reloads
                        overlayPublisher.overlayDidChange()
                    } catch {
                        print("Save error: \(error)")
                    }
                    dismiss()
                }
            }
        }
        .onAppear {
            setupMapForOverlay()
        }
        .onReceive(Timer.publish(every: 0.5, on: .main, in: .common).autoconnect()) { _ in
            // Auto-update overlay position based on map center
            updateOverlayPosition()
        }
    }

    /// Set up the map to show the overlay at its current position with appropriate zoom
    private func setupMapForOverlay() {
        // Set map center to overlay's saved position
        let overlayCoordinate = CLLocationCoordinate2D(
            latitude: overlay.centerLatitude,
            longitude: overlay.centerLongitude
        )

        // Calculate appropriate zoom level based on overlay scale
        // The overlay scale is in meters, we want it to take up about 60% of the map view height
        let mapHeightInMeters = overlay.relativeScale / 0.6 // Scale up so overlay is 60% of view

        // Set the map region
        viewModel.setMapRegion(
            center: overlayCoordinate,
            latitudinalMeters: mapHeightInMeters,
            longitudinalMeters: mapHeightInMeters
        )

        print("Map centered on overlay at: \(overlayCoordinate.latitude), \(overlayCoordinate.longitude)")
        print("Map zoom set for overlay scale: \(overlay.relativeScale)m -> view scale: \(mapHeightInMeters)m")
    }

    /// Auto-update overlay position based on current map center
    private func updateOverlayPosition() {
        guard let centerCoordinate = viewModel.centerCoordinate else { return }

        // Only update if the position has changed significantly (avoid constant updates)
        let latDiff = abs(overlay.centerLatitude - centerCoordinate.latitude)
        let lonDiff = abs(overlay.centerLongitude - centerCoordinate.longitude)

        if latDiff > 0.00001 || lonDiff > 0.00001 {
            overlay.centerLatitude = centerCoordinate.latitude
            overlay.centerLongitude = centerCoordinate.longitude

            // Save the position change
            do {
                try viewContext.save()
            } catch {
                print("Error auto-saving overlay position: \(error)")
            }
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let overlay = Overlay(context: context)
    overlay.name = "Sample"
    overlay.imageURL = URL(fileURLWithPath: "/dev/null")
    return NavigationStack { OverlayEditView(overlay: overlay, viewModel: MapViewModel()) }
        .environment(\.managedObjectContext, context)
}
