import SwiftUI
import CoreData

struct OverlayEditView: View {
    @ObservedObject var overlay: Overlay
    var viewModel: MapViewModel
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    // Add the new state variable here
    @State private var isEditingPosition: Bool = true

    var body: some View {
        VStack(spacing: 0) {
            // Map view - takes up most of the screen but leaves room for controls
            MapViewRepresentable(viewModel: viewModel, overlays: [overlay], isEditingPosition: isEditingPosition)
                .frame(maxWidth: .infinity)
                .frame(height: UIScreen.main.bounds.height * 0.6) // Use 60% of screen height
                .clipped()

            // Controls section - fixed height at bottom
            VStack(spacing: 12) {
                // Text fields
                VStack(spacing: 8) {
                    TextField("Name", text: Binding(
                        get: { overlay.name ?? "" },
                        set: { overlay.name = $0 }
                    ))
                    .textFieldStyle(.roundedBorder)

                    TextField("Description", text: Binding(
                        get: { overlay.overlayDescription ?? "" },
                        set: { overlay.overlayDescription = $0 }
                    ))
                    .textFieldStyle(.roundedBorder)
                }

                // Sliders
                VStack(spacing: 8) {
                    HStack {
                        Text("Opacity")
                            .frame(width: 60, alignment: .leading)
                        Slider(value: Binding(
                            get: { overlay.opacity },
                            set: {
                                overlay.opacity = $0
                                // Trigger immediate visual update
                                overlayPublisher.overlayDidChange()
                            }
                        ), in: 0...1)
                        Text(String(format: "%.1f", overlay.opacity))
                            .frame(width: 35)
                            .font(.caption)
                    }

                    HStack {
                        Text("Rotation")
                            .frame(width: 60, alignment: .leading)
                        Slider(value: Binding(
                            get: { overlay.rotation },
                            set: {
                                overlay.rotation = $0
                                // Trigger immediate visual update
                                overlayPublisher.overlayDidChange()
                            }
                        ), in: 0...(Double.pi * 2))
                        Text(String(format: "%.0f°", overlay.rotation * 180 / .pi))
                            .frame(width: 35)
                            .font(.caption)
                    }

                    HStack {
                        Text("Scale")
                            .frame(width: 60, alignment: .leading)
                        Slider(value: Binding(
                            get: { overlay.relativeScale },
                            set: {
                                overlay.relativeScale = $0
                                // Trigger immediate visual update
                                overlayPublisher.overlayDidChange()
                            }
                        ), in: 100...10000)
                        Text(String(format: "%.0fm", overlay.relativeScale))
                            .frame(width: 35)
                            .font(.caption)
                    }
                }

                // Position editing button
                Button(isEditingPosition ? "Stop Editing Position" : "Edit Position") {
                    isEditingPosition.toggle()
                }
                .buttonStyle(.borderedProminent)
            }
            .padding()
            .background(.regularMaterial)
        }
        .navigationTitle("Edit Overlay")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    if isEditingPosition {
                        // Get map center from the viewModel using the public centerCoordinate property
                        if let centerCoordinate = viewModel.centerCoordinate {
                            overlay.centerLatitude = centerCoordinate.latitude
                            overlay.centerLongitude = centerCoordinate.longitude
                            print("Overlay position updated to map center: \(centerCoordinate.latitude), \(centerCoordinate.longitude)")
                        } else {
                            print("Error: Center coordinate not available from viewModel.")
                        }
                        // Optionally, set isEditingPosition to false if you want to switch modes after saving
                        // self.isEditingPosition = false
                    }
                    overlay.updatedAt = Date() // Keep this
                    do {
                        try viewContext.save()
                        print("Overlay saved.")

                        // Notify that the overlay has changed so Map Screen reloads
                        overlayPublisher.overlayDidChange()
                    } catch {
                        print("Save error: \(error)")
                    }
                    dismiss() // Keep this
                }
            }
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let overlay = Overlay(context: context)
    overlay.name = "Sample"
    overlay.imageURL = URL(fileURLWithPath: "/dev/null")
    return NavigationStack { OverlayEditView(overlay: overlay, viewModel: MapViewModel()) }
        .environment(\.managedObjectContext, context)
}
