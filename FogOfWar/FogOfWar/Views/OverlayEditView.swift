import SwiftUI
import CoreData

struct OverlayEditView: View {
    @ObservedObject var overlay: Overlay
    var viewModel: MapViewModel
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    // Add the new state variable here
    @State private var isEditingPosition: Bool = true

    var body: some View {
        ZStack(alignment: .bottom) {
            MapViewRepresentable(viewModel: viewModel, overlays: [overlay], isEditingPosition: isEditingPosition) // Pass the flag
                .ignoresSafeArea()
            VStack(spacing: 8) {
                TextField("Name", text: Binding(
                    get: { overlay.name ?? "" },
                    set: { overlay.name = $0 }
                ))
                .textFieldStyle(.roundedBorder)
                TextField("Description", text: Binding(
                    get: { overlay.overlayDescription ?? "" },
                    set: { overlay.overlayDescription = $0 }
                ))
                .textFieldStyle(.roundedBorder)
                HStack {
                    Text("Opacity")
                    Slider(value: Binding(
                        get: { overlay.opacity },
                        set: { overlay.opacity = $0 }
                    ), in: 0...1)
                }
                HStack {
                    Text("Rotation")
                    Slider(value: Binding(
                        get: { overlay.rotation },
                        set: { overlay.rotation = $0 }
                    ), in: 0...(Double.pi * 2))
                }
                HStack {
                    Text("Scale")
                    Slider(value: Binding(
                        get: { overlay.relativeScale },
                        set: { overlay.relativeScale = $0 }
                    ), in: 100...10000)
                }
            }
            .padding()
            .background(.regularMaterial)
            .frame(maxWidth: .infinity)
        }
        .navigationTitle("Edit Overlay")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    if isEditingPosition {
                        // Get map center from the viewModel
                        // The viewModel.mapView should be the MKMapView instance
                        if let mapView = viewModel.mapView {
                            let centerCoordinate = mapView.centerCoordinate
                            overlay.centerLatitude = centerCoordinate.latitude
                            overlay.centerLongitude = centerCoordinate.longitude
                            print("Overlay position updated to map center: \(centerCoordinate.latitude), \(centerCoordinate.longitude)")
                        } else {
                            print("Error: MapView not available in viewModel to get center coordinate.")
                        }
                        // Optionally, set isEditingPosition to false if you want to switch modes after saving
                        // self.isEditingPosition = false
                    }
                    overlay.updatedAt = Date() // Keep this
                    do {
                        try viewContext.save()
                        print("Overlay saved.")
                    } catch {
                        print("Save error: \(error)")
                    }
                    dismiss() // Keep this
                }
            }
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let overlay = Overlay(context: context)
    overlay.name = "Sample"
    overlay.imageURL = URL(fileURLWithPath: "/dev/null")
    return NavigationStack { OverlayEditView(overlay: overlay, viewModel: MapViewModel()) }
        .environment(\.managedObjectContext, context)
}
