import SwiftUI
import CoreLocation
import CoreData

struct OverlayEditView: View {
    @ObservedObject var overlay: Overlay
    var viewModel: MapViewModel
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    // Always in editing mode - no toggle needed

    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Map view - always in editing mode for simplicity
                MapViewRepresentable(viewModel: viewModel, overlays: [overlay], isEditingPosition: true)
                    .frame(maxWidth: .infinity)
                    .frame(height: geometry.size.height - 280) // Leave 280pt for controls and safe areas
                    .clipped()

                // Controls section - scrollable if needed
                ScrollView {
                    VStack(spacing: 12) {
                        // Text fields
                        VStack(spacing: 8) {
                            TextField("Name", text: Binding(
                                get: { overlay.name ?? "" },
                                set: { overlay.name = $0 }
                            ))
                            .textFieldStyle(.roundedBorder)

                            TextField("Description", text: Binding(
                                get: { overlay.overlayDescription ?? "" },
                                set: { overlay.overlayDescription = $0 }
                            ))
                            .textFieldStyle(.roundedBorder)
                        }

                        // Sliders
                        VStack(spacing: 8) {
                            HStack {
                                Text("Opacity")
                                    .frame(width: 60, alignment: .leading)
                                Slider(value: Binding(
                                    get: { overlay.opacity },
                                    set: {
                                        overlay.opacity = $0
                                        // Trigger immediate visual update
                                        overlayPublisher.overlayDidChange()
                                    }
                                ), in: 0...1)
                                Text(String(format: "%.1f", overlay.opacity))
                                    .frame(width: 35)
                                    .font(.caption)
                            }

                            HStack {
                                Text("Rotation")
                                    .frame(width: 60, alignment: .leading)
                                Slider(value: Binding(
                                    get: { overlay.rotation },
                                    set: {
                                        overlay.rotation = $0
                                        // Trigger immediate visual update
                                        overlayPublisher.overlayDidChange()
                                    }
                                ), in: 0...(Double.pi * 2))
                                Text(String(format: "%.0f°", overlay.rotation * 180 / .pi))
                                    .frame(width: 35)
                                    .font(.caption)
                            }

                            HStack {
                                Text("Scale")
                                    .frame(width: 60, alignment: .leading)
                                Slider(value: Binding(
                                    get: { overlay.relativeScale },
                                    set: {
                                        overlay.relativeScale = $0
                                        // Trigger immediate visual update
                                        overlayPublisher.overlayDidChange()
                                    }
                                ), in: 100...10000)
                                Text(String(format: "%.0fm", overlay.relativeScale))
                                    .frame(width: 35)
                                    .font(.caption)
                            }
                        }

                        // Update position button - capture current map center
                        Button("Update Position") {
                            // Capture the current map center as the new position
                            if let centerCoordinate = viewModel.centerCoordinate {
                                overlay.centerLatitude = centerCoordinate.latitude
                                overlay.centerLongitude = centerCoordinate.longitude
                                print("Overlay position updated to: \(centerCoordinate.latitude), \(centerCoordinate.longitude)")

                                // Save immediately
                                do {
                                    try viewContext.save()
                                    overlayPublisher.overlayDidChange()
                                    print("Overlay position saved successfully")
                                } catch {
                                    print("Error saving overlay position: \(error)")
                                }
                            } else {
                                print("Error: Could not get current map center")
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                }
                .frame(maxHeight: 280) // Fixed max height for controls
                .background(.regularMaterial)
            }
        }
        .navigationTitle("Edit Overlay")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    // Save any remaining changes (position is already saved when stopping editing)
                    overlay.updatedAt = Date()
                    do {
                        try viewContext.save()
                        print("Overlay saved.")

                        // Notify that the overlay has changed so Map Screen reloads
                        overlayPublisher.overlayDidChange()
                    } catch {
                        print("Save error: \(error)")
                    }
                    dismiss()
                }
            }
        }
        .onAppear {
            setupMapForOverlay()
        }
    }

    /// Set up the map to show the overlay at its current position with appropriate zoom
    private func setupMapForOverlay() {
        // Set map center to overlay's saved position
        let overlayCoordinate = CLLocationCoordinate2D(
            latitude: overlay.centerLatitude,
            longitude: overlay.centerLongitude
        )

        // Calculate appropriate zoom level based on overlay scale
        // The overlay scale is in meters, we want it to take up about 60% of the map view height
        let mapHeightInMeters = overlay.relativeScale / 0.6 // Scale up so overlay is 60% of view

        // Set the map region
        viewModel.setMapRegion(
            center: overlayCoordinate,
            latitudinalMeters: mapHeightInMeters,
            longitudinalMeters: mapHeightInMeters
        )

        print("Map centered on overlay at: \(overlayCoordinate.latitude), \(overlayCoordinate.longitude)")
        print("Map zoom set for overlay scale: \(overlay.relativeScale)m -> view scale: \(mapHeightInMeters)m")
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let overlay = Overlay(context: context)
    overlay.name = "Sample"
    overlay.imageURL = URL(fileURLWithPath: "/dev/null")
    return NavigationStack { OverlayEditView(overlay: overlay, viewModel: MapViewModel()) }
        .environment(\.managedObjectContext, context)
}
