import SwiftUI
import CoreData
import PhotosUI
import UniformTypeIdentifiers

struct OverlayListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Overlay.zOrder, ascending: true)],
        animation: .default)
    private var overlays: FetchedResults<Overlay>

    @State private var showingImagePicker = false
    @State private var selectedItem: PhotosPickerItem?
    @State private var editingOverlay: Overlay?

    var body: some View {
        List {
            ForEach(overlays, id: \.id) { overlay in
                OverlayRowView(overlay: overlay) {
                    toggleVisibility(overlay)
                }
                .padding(.vertical, 4)
            }
            .onDelete(perform: deleteOverlays)
            .onMove(perform: moveOverlays)
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                addButton
            }
        }
        .onChange(of: selectedItem) { _, newItem in
            Task {
                if let newItem = newItem {
                    await loadSelectedImage(newItem)
                }
            }
        }
    }

    // MARK: - Subviews

    private var addButton: some View {
        PhotosPicker(
            selection: $selectedItem,
            matching: .images,
            photoLibrary: .shared()
        ) {
            Image(systemName: "plus")
        }
    }

    // MARK: - Actions

    private func toggleVisibility(_ overlay: Overlay) {
        withAnimation {
            overlay.isVisible.toggle()
            saveContext()
            overlayPublisher.overlayDidChange()
        }
    }



    private func deleteOverlays(offsets: IndexSet) {
        withAnimation {
            offsets.map { overlays[$0] }.forEach(viewContext.delete)
            saveContext()
            overlayPublisher.overlayDidChange()
        }
    }

    private func moveOverlays(from source: IndexSet, to destination: Int) {
        // Update z-order based on new positions
        var overlayArray = Array(overlays)
        overlayArray.move(fromOffsets: source, toOffset: destination)

        for (index, overlay) in overlayArray.enumerated() {
            overlay.zOrder = Int32(index)
        }

        saveContext()
        overlayPublisher.overlayDidChange()
    }

    private func saveContext() {
        do {
            try viewContext.save()
        } catch {
            print("Error saving context: \(error)")
        }
    }

    // MARK: - Image Loading

    private func loadSelectedImage(_ item: PhotosPickerItem) async {
        guard let data = try? await item.loadTransferable(type: Data.self),
              let image = UIImage(data: data) else {
            print("Failed to load selected image")
            return
        }

        await MainActor.run {
            addOverlay(image: image)
        }
    }

    private func addOverlay(image: UIImage) {
        withAnimation {
            let newOverlay = Overlay(context: viewContext)
            newOverlay.id = UUID()
            newOverlay.createdAt = Date()
            newOverlay.updatedAt = Date()
            newOverlay.isVisible = true
            newOverlay.opacity = 0.7
            newOverlay.relativeScale = 1000.0 // Default 1km scale
            newOverlay.rotation = 0.0
            newOverlay.zOrder = Int32(overlays.count)

            // Set default location (47.073692, -80.103519)
            newOverlay.centerLatitude = 47.073692
            newOverlay.centerLongitude = -80.103519

            // Save the image using ImageManager
            if let imageURL = ImageManager.shared.saveImage(image, withID: newOverlay.id) {
                newOverlay.imageURL = imageURL
            }

            saveContext()
            overlayPublisher.overlayDidChange()
        }
    }
}

// MARK: - OverlayRowView
struct OverlayRowView: View {
    let overlay: Overlay
    let onToggleVisibility: () -> Void

    var body: some View {
        HStack {
            visibilityButton
            overlayInfo
            Spacer()
            editButton
        }
    }

    private var visibilityButton: some View {
        Button(action: onToggleVisibility) {
            Image(systemName: overlay.isVisible ? "eye.fill" : "eye.slash.fill")
                .foregroundColor(overlay.isVisible ? .blue : .gray)
        }
        .buttonStyle(BorderlessButtonStyle())
    }

    private var overlayInfo: some View {
        HStack(spacing: 12) {
            thumbnailView
            detailsView
        }
    }

    private var thumbnailView: some View {
        Group {
            if let image = ImageManager.shared.loadImage(from: overlay.imageURL) {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 44, height: 44)
                    .clipped()
                    .cornerRadius(6)
            } else {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 44, height: 44)
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    )
            }
        }
    }

    private var detailsView: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(overlay.name ?? "Overlay \(itemFormatter.string(from: overlay.createdAt ?? Date()))")
                .font(.headline)
                .lineLimit(1)

            HStack(spacing: 8) {
                Text("Scale: \(Int(overlay.relativeScale))m")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("Opacity: \(Int(overlay.opacity * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }

    private var editButton: some View {
        NavigationLink(destination: OverlayEditView(overlay: overlay, viewModel: MapViewModel())) {
            Image(systemName: "pencil")
                .foregroundColor(.blue)
        }
        .buttonStyle(BorderlessButtonStyle())
    }
}

// MARK: - Date Formatter
private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .medium
    return formatter
}()
