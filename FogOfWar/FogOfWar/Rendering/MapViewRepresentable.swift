import SwiftUI
import MapKit

struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var viewModel: MapViewModel
    var overlays: [Overlay]
    var isEditingPosition: Bool // Add this new property

    // MARK: - Coordinator
    class Coordinator: NSObject {
        var parent: MapViewRepresentable
        var draggedOverlay: Overlay?
        var panGestureRecognizer: UIPanGestureRecognizer? // Keep a reference to remove/add it

        init(_ parent: MapViewRepresentable) {
            self.parent = parent
        }

        @objc func handlePan(_ gestureRecognizer: UIPanGestureRecognizer) {
            // This function should only be active if NOT in isEditingPosition mode
            guard !parent.isEditingPosition, let mapView = gestureRecognizer.view as? MKMapView else {
                // If in editing position mode, ensure map interaction is enabled
                if parent.isEditingPosition, let mapView = gestureRecognizer.view as? MKMapView {
                    mapView.isScrollEnabled = true
                    mapView.isZoomEnabled = true
                }
                return
            }

            switch gestureRecognizer.state {
            case .began:
                let touchLocation = gestureRecognizer.location(in: mapView)
                // Iterate through the MKOverlays currently on the map
                for currentMapKitOverlay in mapView.overlays {
                    // Ensure it's our custom ImageOverlay type
                    guard let imageOverlay = currentMapKitOverlay as? ImageOverlay else { continue }

                    // Check if this overlay is one of the visible overlays managed by our parent
                    // and that its data is the one we are looking for.
                    guard parent.overlays.contains(where: { $0 == imageOverlay.overlayData && $0.isVisible }) else { continue }

                    // Get the renderer for the overlay
                    if let renderer = mapView.renderer(for: currentMapKitOverlay) {
                        // Get the frame of the overlay in the view's coordinates
                        let overlayFrameInView = renderer.rect(for: currentMapKitOverlay.boundingMapRect)

                        if overlayFrameInView.contains(touchLocation) {
                            draggedOverlay = imageOverlay.overlayData // This is the Core Data Overlay object
                            // Disable map interaction when an overlay drag begins
                            mapView.isScrollEnabled = false
                            mapView.isZoomEnabled = false
                            break
                        }
                    }
                }
            case .changed:
                if let draggedOverlay = draggedOverlay {
                    let touchLocation = gestureRecognizer.location(in: mapView)
                    let newCoordinate = mapView.convert(touchLocation, toCoordinateFrom: mapView)

                    // Update Core Data object properties on the main thread
                    DispatchQueue.main.async {
                        draggedOverlay.centerLatitude = newCoordinate.latitude
                        draggedOverlay.centerLongitude = newCoordinate.longitude
                    }
                }
            case .ended, .cancelled:
                draggedOverlay = nil
                // Always re-enable map interaction when the gesture ends or is cancelled
                mapView.isScrollEnabled = true
                mapView.isZoomEnabled = true
            default:
                break
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView(frame: .zero)
        mapView.showsUserLocation = true
        mapView.delegate = viewModel
        viewModel.setMapView(mapView)

        // Create the pan gesture recognizer
        let panGesture = UIPanGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handlePan(_:)))
        context.coordinator.panGestureRecognizer = panGesture // Store it

        // Add gesture recognizer IF NOT in editing position mode initially
        if !isEditingPosition {
            mapView.addGestureRecognizer(panGesture)
        } else {
            // Ensure map is interactive if in editing mode
            mapView.isScrollEnabled = true
            mapView.isZoomEnabled = true
        }

        return mapView
    }

    func updateUIView(_ uiView: MKMapView, context: Context) {
        if uiView.delegate !== viewModel {
            uiView.delegate = viewModel
        }

        // Manage the pan gesture recognizer based on isEditingPosition
        if let panGesture = context.coordinator.panGestureRecognizer {
            if isEditingPosition {
                if uiView.gestureRecognizers?.contains(panGesture) == true {
                    uiView.removeGestureRecognizer(panGesture)
                    print("Overlay pan gesture REMOVED (editing position)")
                }
                // Ensure map interaction is enabled when in editing mode
                uiView.isScrollEnabled = true
                uiView.isZoomEnabled = true
            } else {
                if uiView.gestureRecognizers?.contains(panGesture) == false {
                    uiView.addGestureRecognizer(panGesture)
                    print("Overlay pan gesture ADDED (not editing position)")
                }
            }
        }

        // Always remove all *map* overlays first
        uiView.removeOverlays(uiView.overlays.filter { $0 is ImageOverlay })


        guard viewModel.showOverlays else { return }

        if isEditingPosition {
            // If editing position, we want the *edited* overlay to appear centered.
            // We assume the first overlay in the `overlays` array is the one being edited.
            if let overlayToEdit = overlays.first, overlayToEdit.isVisible {
                // Create a temporary, mutable copy of the overlay data or directly modify a temporary ImageOverlay instance
                // For simplicity, we'll create a new ImageOverlay instance whose coordinate is always the map's center.
                // The actual CoreData object `overlayToEdit.centerLatitude/Longitude` is NOT changed here.
                // That only happens on Save (Step 4).

                let centeredOverlay = PositionPreviewOverlay(overlayData: overlayToEdit, mapCenter: uiView.centerCoordinate)
                uiView.addOverlay(centeredOverlay)
                print("Added PositionPreviewOverlay for: \(overlayToEdit.name ?? "Unnamed") at map center")

                // Add other non-edited overlays normally, if any (though typically OverlayEditView passes only one)
                for overlay in overlays.dropFirst() where overlay.isVisible {
                    let mapOverlay = ImageOverlay(overlayData: overlay)
                    uiView.addOverlay(mapOverlay)
                    print("Added regular overlay: \(overlay.name ?? "Unnamed")")
                }
            }
        } else {
            // Standard behavior: add all overlays based on their stored coordinates
            for overlay in overlays where overlay.isVisible {
                let mapOverlay = ImageOverlay(overlayData: overlay)
                uiView.addOverlay(mapOverlay)
                // print("Added overlay: \(overlay.name ?? "Unnamed") at \(overlay.centerLatitude), \(overlay.centerLongitude)") // Already in original code
            }
        }
    }
}

// Define a new MKOverlay type or modify ImageOverlay to take a dynamic coordinate for this preview.
// A new type is cleaner to not pollute ImageOverlay with isEditingPosition logic.

class PositionPreviewOverlay: ImageOverlay {
    private var currentMapCenter: CLLocationCoordinate2D

    init(overlayData: Overlay, mapCenter: CLLocationCoordinate2D) {
        self.currentMapCenter = mapCenter
        // Initialize ImageOverlay with the actual overlayData, but we'll override the coordinate.
        super.init(overlayData: overlayData)
    }

    override var coordinate: CLLocationCoordinate2D {
        return currentMapCenter // Always use the map's current center for this preview
    }

    /// Update the map center coordinate for this preview overlay
    func updateMapCenter(_ newCenter: CLLocationCoordinate2D) {
        currentMapCenter = newCenter
        clearBoundsCache() // Clear cached bounds since position changed
    }

    // We need to invalidate the boundingMapRect cache when the coordinate changes.
    // The map view should re-query boundingMapRect when its center changes if the overlay is re-added.
    // Or, more robustly, the renderer should get the coordinate directly if possible,
    // but MKOverlayRenderer typically relies on the overlay's boundingMapRect.
    // Forcing re-creation of the overlay in updateUIView on map move isn't ideal.
    // A better way is to ensure that when the map region changes, updateUIView is triggered,
    // and we create a new PositionPreviewOverlay with the new map center.
    // MKMapView's delegate method `mapViewDidChangeVisibleRegion` can be used by the ViewModel
    // to trigger a SwiftUI view update, which in turn calls updateUIView.

    // Forcing boundingMapRect to recompute based on currentMapCenter:
    override var boundingMapRect: MKMapRect {
        let center = MKMapPoint(currentMapCenter)
        let metersPerPoint = MKMetersPerMapPointAtLatitude(currentMapCenter.latitude)
        // Use overlayData.relativeScale for size, as that's what the user is adjusting
        let sizeInPoints = overlayData.relativeScale / metersPerPoint
        let rect = MKMapRect(
            x: center.x - sizeInPoints / 2,
            y: center.y - sizeInPoints / 2,
            width: sizeInPoints,
            height: sizeInPoints
        )
        return rect
    }
}

/// Custom MKOverlay implementation for image overlays
class ImageOverlay: NSObject, MKOverlay {
    // The Core Data overlay entity
    let overlayData: Overlay

    // Cached image for performance
    private var cachedImage: UIImage?

    // Cached bounds
    private var cachedBounds: MKMapRect?

    init(overlayData: Overlay) {
        self.overlayData = overlayData
        super.init()
    }

    // MARK: - MKOverlay Protocol

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: overlayData.centerLatitude, longitude: overlayData.centerLongitude)
    }

    var boundingMapRect: MKMapRect {
        // If we have cached bounds, use them
        if let cachedBounds = cachedBounds {
            return cachedBounds
        }

        // Calculate bounds based on the overlay's scale and position
        let center = MKMapPoint(coordinate)

        // Convert scale from meters to map points
        let metersPerPoint = MKMetersPerMapPointAtLatitude(coordinate.latitude)
        let sizeInPoints = overlayData.relativeScale / metersPerPoint

        // Create the bounding rect
        let rect = MKMapRect(
            x: center.x - sizeInPoints / 2,
            y: center.y - sizeInPoints / 2,
            width: sizeInPoints,
            height: sizeInPoints
        )

        // Cache this rect
        cachedBounds = rect
        return rect
    }

    // MARK: - Image Loading

    /// Get the image for this overlay, loading it if necessary
    func getImage() -> UIImage? {
        if let cachedImage = cachedImage {
            return cachedImage
        }

        // Load the image using ImageManager
        if let image = ImageManager.shared.loadImage(from: overlayData.imageURL) {
            cachedImage = image
            return image
        }

        return nil
    }

    /// Clear the cached image to force reload
    func clearImageCache() {
        cachedImage = nil
    }

    /// Clear the cached bounds to force recalculation
    func clearBoundsCache() {
        cachedBounds = nil
    }
}

/// Renderer for image overlays with improved performance and transformation support
class ImageOverlayRenderer: MKOverlayRenderer {

    override init(overlay: MKOverlay) {
        super.init(overlay: overlay)
    }

    override func draw(_ mapRect: MKMapRect, zoomScale: MKZoomScale, in context: CGContext) {
        guard let imageOverlay = overlay as? ImageOverlay else {
            print("ImageOverlayRenderer: Overlay is not an ImageOverlay")
            return
        }

        guard let image = imageOverlay.getImage() else {
            print("ImageOverlayRenderer: No image available for overlay '\(imageOverlay.overlayData.name ?? "Unnamed")'")
            return
        }

        // Get the rect for the overlay in the current view
        let overlayRect = rect(for: overlay.boundingMapRect)

        // Check if the overlay rect intersects with the map rect being drawn
        let drawRect = rect(for: mapRect)
        guard overlayRect.intersects(drawRect) else {
            return // No need to draw if not visible
        }

        // Save the graphics state
        context.saveGState()

        // Set up the transformation
        setupTransformation(context: context, overlayRect: overlayRect, imageOverlay: imageOverlay)

        // Set the opacity
        context.setAlpha(CGFloat(imageOverlay.overlayData.opacity))

        // Draw the image
        if let cgImage = image.cgImage {
            // Calculate the image rect (centered and scaled)
            let imageSize = image.size
            let aspectRatio = imageSize.width / imageSize.height

            var imageRect: CGRect
            if aspectRatio > 1.0 {
                // Landscape image
                let height = overlayRect.height
                let width = height * aspectRatio
                imageRect = CGRect(
                    x: -width / 2,
                    y: -height / 2,
                    width: width,
                    height: height
                )
            } else {
                // Portrait or square image
                let width = overlayRect.width
                let height = width / aspectRatio
                imageRect = CGRect(
                    x: -width / 2,
                    y: -height / 2,
                    width: width,
                    height: height
                )
            }

            context.draw(cgImage, in: imageRect)
        }

        // Restore the graphics state
        context.restoreGState()
    }

    /// Set up the transformation matrix for the overlay
    private func setupTransformation(context: CGContext, overlayRect: CGRect, imageOverlay: ImageOverlay) {
        // Move to the center of the overlay
        context.translateBy(x: overlayRect.midX, y: overlayRect.midY)

        // Apply rotation if any
        let rotation = imageOverlay.overlayData.rotation
        if rotation != 0 {
            context.rotate(by: CGFloat(rotation))
        }

        // Apply any additional scaling based on the relative scale
        let scale = CGFloat(imageOverlay.overlayData.relativeScale / 1000.0) // Normalize scale
        if scale != 1.0 && scale > 0 {
            context.scaleBy(x: scale, y: scale)
        }
    }
}
